package api

import (
	"github.com/gin-gonic/gin"

	"wnapi/internal/core"
	"wnapi/internal/pkg/logger"
	"wnapi/modules/website/api/handlers"
	"wnapi/modules/website/service"
)

// Handler là đối tượng ch<PERSON>h xử lý API cho module Website
type Handler struct {
	websiteHandler   *handlers.WebsiteHandler
	pageHandler      *handlers.PageHandler
	themeHandler     *handlers.ThemeHandler
	templateHandler  *handlers.TemplateHandler
	logger           logger.Logger
	routes           []string
}

// NewHandler tạo một handler mới
func NewHandler(websiteService *service.Service, log logger.Logger) *Handler {
	return &Handler{
		websiteHandler:  handlers.NewWebsiteHandler(websiteService.WebsiteService()),
		pageHandler:     handlers.NewPageHandler(websiteService.PageService()),
		themeHandler:    handlers.NewThemeHandler(websiteService.ThemeService()),
		templateHandler: handlers.NewTemplateHandler(websiteService.TemplateService()),
		logger:          log,
		routes:          []string{},
	}
}

// RegisterRoutes đăng ký tất cả routes cho module Website
func (h *Handler) RegisterRoutes(server *core.Server) error {
	h.logger.Info("Đăng ký routes cho module Website")
	
	// Lấy router engine từ server
	router := server.GetRouter()
	
	// Tạo API group
	api := router.Group("/api/v1/website")
	
	// Website routes
	websites := api.Group("/websites")
	{
		websites.GET("", h.websiteHandler.ListWebsites)
		websites.POST("", h.websiteHandler.CreateWebsite)
		websites.GET("/:id", h.websiteHandler.GetWebsite)
		websites.PUT("/:id", h.websiteHandler.UpdateWebsite)
		websites.DELETE("/:id", h.websiteHandler.DeleteWebsite)
	}

	// Page routes - Sử dụng path riêng biệt để tránh xung đột
	websitePages := api.Group("/website-pages/:website_id")
	{
		websitePages.GET("", h.pageHandler.ListPages)
		websitePages.POST("", h.pageHandler.CreatePage)
		websitePages.GET("/page/:id", h.pageHandler.GetPage)
		websitePages.PUT("/page/:id", h.pageHandler.UpdatePage)
		websitePages.DELETE("/page/:id", h.pageHandler.DeletePage)
		websitePages.GET("/homepage", h.pageHandler.GetHomepage)
		websitePages.GET("/by-slug/:slug", h.pageHandler.GetPageBySlug)
	}

	// Theme routes
	themes := api.Group("/themes")
	{
		themes.GET("", h.themeHandler.ListThemes)
		themes.POST("", h.themeHandler.CreateTheme)
		themes.GET("/:id", h.themeHandler.GetTheme)
		themes.PUT("/:id", h.themeHandler.UpdateTheme)
		themes.DELETE("/:id", h.themeHandler.DeleteTheme)
		themes.GET("/public", h.themeHandler.ListPublicThemes)
	}

	// Template routes
	templates := api.Group("/theme-templates/:theme_id")
	{
		templates.GET("", h.templateHandler.ListTemplates)
		templates.POST("", h.templateHandler.CreateTemplate)
		templates.GET("/template/:id", h.templateHandler.GetTemplate)
		templates.PUT("/template/:id", h.templateHandler.UpdateTemplate)
		templates.DELETE("/template/:id", h.templateHandler.DeleteTemplate)
		templates.GET("/by-type/:type", h.templateHandler.ListTemplatesByType)
	}
	
	// Record routes for health check
	h.routes = []string{
		"/api/v1/website/websites",
		"/api/v1/website/website-pages/:website_id",
		"/api/v1/website/themes",
		"/api/v1/website/theme-templates/:theme_id",
	}
	
	// Add health check endpoint
	api.GET("/health", h.healthCheck)
	
	return nil
}

// healthCheck là endpoint kiểm tra trạng thái hoạt động của module
func (h *Handler) healthCheck(c *gin.Context) {
	c.JSON(200, gin.H{
		"status":  "ok",
		"module":  "website",
		"routes":  h.routes,
		"message": "Website module is working properly",
	})
}
