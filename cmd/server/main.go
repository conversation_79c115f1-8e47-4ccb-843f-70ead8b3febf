package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"
	"wnapi/internal/bootstrap"
	"wnapi/internal/core"
	"wnapi/internal/pkg/cache/memorycache"
	"wnapi/internal/pkg/config/viperconfig"
	"wnapi/internal/pkg/database/gormdb"
	"wnapi/internal/pkg/logger/zaplogger"

	// Import modules để đăng ký với registry
	_ "wnapi/modules/auth"
	_ "wnapi/modules/hello"

	//_ "wnapi/modules/media"
	_ "wnapi/modules/notification"
	_ "wnapi/modules/product"
	_ "wnapi/modules/rbac"

	// Import plugins để đăng ký với registry
	_ "wnapi/plugins/logger"
)

var (
	configFlag  = flag.String("config", ".env", "Path to .env file")
	projectFlag = flag.String("project", "", "Project to run")
	debugFlag   = flag.Bool("debug", false, "Enable debug mode")
)

func main() {
	// 1. Khởi tạo config
	cfg, err := viperconfig.NewConfigLoader().Load("./config/app.yaml")
	if err != nil {
		log.Fatalf("Lỗi khởi tạo config: %v", err)
	}

	// 2. Khởi tạo logger
	appLogger, err := zaplogger.NewLogger(
		cfg.GetString("logger.level"),
		cfg.GetString("logger.format"),
	)
	if err != nil {
		log.Fatalf("Lỗi khởi tạo logger: %v", err)
	}

	// 3. Khởi tạo database
	dbManager, err := gormdb.NewGORMDBManager(
		cfg.GetString("database.dsn_write"),
		cfg.GetString("database.dsn_read"),
		appLogger,
	)
	if err != nil {
		appLogger.Error("Lỗi khởi tạo DB manager", "error", err)
		os.Exit(1)
	}

	// 4. Khởi tạo cache
	appCache := memorycache.NewMemoryCache()
	appLogger.Info("Cache initialized", "type", "in-memory")

	// 5. Bootstrap RBAC và Permission
	mwFactory, err := bootstrap.BootstrapRBAC(
		dbManager.DB(),
		appCache,
		appLogger,
		cfg,
	)
	if err != nil {
		appLogger.Error("Lỗi khởi tạo RBAC components", "error", err)
		os.Exit(1)
	}
	appLogger.Info("RBAC Permission system initialized")

	// 6. Khởi tạo App
	appInstance := core.NewAppBootstrap(
		cfg,
		appLogger,
		dbManager,
		appCache,
		mwFactory,
	)

	// 7. Khởi tạo Server
	server := core.NewServer(appInstance)
	appLogger.Info("HTTP Server initialized")

	// 8. Khởi động server trong goroutine
	go func() {
		if err := server.Start(); err != nil {
			appLogger.Error("Lỗi chạy server", "error", err)
			os.Exit(1)
		}
	}()

	// 9. Graceful shutdown
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	appLogger.Info("Shutting down server...")
	ctx, cancel := context.WithTimeout(context.Background(), cfg.GetDuration("server.shutdown_timeout"))
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		appLogger.Error("Lỗi shutdown server", "error", err)
	}

	appLogger.Info("Server shutdown complete")
}

// printModuleInfo hiển thị thông tin về các module đã nạp
func printModuleInfo(app *core.App) {
	modules := app.GetModules()
	if len(modules) == 0 {
		fmt.Println("No modules loaded")
		return
	}

	fmt.Printf("\n=== Loaded Modules (%d) ===\n", len(modules))
	for _, module := range modules {
		fmt.Printf("- %s\n", module.Name())
	}

	// Hiển thị các plugin đã nạp
	plugins := app.GetPlugins()
	if len(plugins) > 0 {
		fmt.Printf("\n=== Loaded Plugins (%d) ===\n", len(plugins))
		for _, plugin := range plugins {
			fmt.Printf("- %s\n", plugin.Name())
		}
	}
}
