// internal/pkg/permission/middleware.go
package permission

// File này sẽ chứa các middleware helpers bổ sung
// Các middleware chính đã được chuyển sang factory.go

import (
	"net/http"
	"strconv"
	"wnapi/internal/pkg/logger"

	"github.com/gin-gonic/gin"
)

// MiddlewareFactory tạo các middleware kiểm tra quyền
type MiddlewareFactory struct {
	checker PermissionChecker
	logger  logger.Logger
}

// NewMiddlewareFactory tạo một instance mới của MiddlewareFactory
func NewMiddlewareFactory(checker PermissionChecker, logger logger.Logger) *MiddlewareFactory {
	return &MiddlewareFactory{
		checker: checker,
		logger:  logger,
	}
}

// RequirePermission trả về middleware yêu cầu quyền cụ thể
func (mf *MiddlewareFactory) RequirePermission(permissionCode string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Lấy tenant ID và user ID từ context
		tenantID, userID, err := getTenantAndUserFromContext(c)
		if err != nil {
			mf.handleUnauthorized(c, "Không thể xác định thông tin xác thực")
			return
		}

		// Kiểm tra quyền
		hasPermission, err := mf.checker.HasPermission(c.Request.Context(), tenantID, userID, permissionCode)
		if err != nil {
			mf.logger.Error("Lỗi kiểm tra quyền", "error", err, "permission", permissionCode)
			mf.handleUnauthorized(c, "Lỗi khi kiểm tra quyền hạn")
			return
		}

		if !hasPermission {
			mf.handleForbidden(c, "Không có quyền thực hiện hành động này")
			return
		}

		c.Next()
	}
}

// RequireAllPermissions trả về middleware yêu cầu tất cả các quyền
func (mf *MiddlewareFactory) RequireAllPermissions(permissionCodes ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Lấy tenant ID và user ID từ context
		tenantID, userID, err := getTenantAndUserFromContext(c)
		if err != nil {
			mf.handleUnauthorized(c, "Không thể xác định thông tin xác thực")
			return
		}

		// Kiểm tra tất cả quyền
		hasPermissions, err := mf.checker.HasAllPermissions(c.Request.Context(), tenantID, userID, permissionCodes...)
		if err != nil {
			mf.logger.Error("Lỗi kiểm tra quyền", "error", err, "permissions", permissionCodes)
			mf.handleUnauthorized(c, "Lỗi khi kiểm tra quyền hạn")
			return
		}

		if !hasPermissions {
			mf.handleForbidden(c, "Không có đủ quyền thực hiện hành động này")
			return
		}

		c.Next()
	}
}

// RequireAnyPermission trả về middleware yêu cầu ít nhất một quyền
func (mf *MiddlewareFactory) RequireAnyPermission(permissionCodes ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Lấy tenant ID và user ID từ context
		tenantID, userID, err := getTenantAndUserFromContext(c)
		if err != nil {
			mf.handleUnauthorized(c, "Không thể xác định thông tin xác thực")
			return
		}

		// Kiểm tra ít nhất một quyền
		hasPermission, err := mf.checker.HasAnyPermission(c.Request.Context(), tenantID, userID, permissionCodes...)
		if err != nil {
			mf.logger.Error("Lỗi kiểm tra quyền", "error", err, "permissions", permissionCodes)
			mf.handleUnauthorized(c, "Lỗi khi kiểm tra quyền hạn")
			return
		}

		if !hasPermission {
			mf.handleForbidden(c, "Không có quyền thực hiện hành động này")
			return
		}

		c.Next()
	}
}

// handleUnauthorized xử lý phản hồi khi không được xác thực
func (mf *MiddlewareFactory) handleUnauthorized(c *gin.Context, message string) {
	c.AbortWithStatusJSON(http.StatusUnauthorized, gin.H{
		"status": gin.H{
			"code":       http.StatusUnauthorized,
			"message":    message,
			"success":    false,
			"error_code": "UNAUTHORIZED",
			"path":       c.Request.URL.Path,
		},
	})
}

// handleForbidden xử lý phản hồi khi không có quyền
func (mf *MiddlewareFactory) handleForbidden(c *gin.Context, message string) {
	c.AbortWithStatusJSON(http.StatusForbidden, gin.H{
		"status": gin.H{
			"code":       http.StatusForbidden,
			"message":    message,
			"success":    false,
			"error_code": "FORBIDDEN",
			"path":       c.Request.URL.Path,
		},
	})
}

// getTenantAndUserFromContext lấy tenant ID và user ID từ context
func getTenantAndUserFromContext(c *gin.Context) (uint, uint, error) {
	// Stub implementation
	tenantIDValue, exists := c.Get("tenantID")
	if !exists {
		return 0, 0, nil
	}

	userIDValue, exists := c.Get("userID")
	if !exists {
		return 0, 0, nil
	}

	tenantID, ok := tenantIDValue.(uint)
	if !ok {
		// Thử chuyển đổi từ string
		if tenantIDStr, ok := tenantIDValue.(string); ok {
			if id, err := strconv.ParseUint(tenantIDStr, 10, 32); err == nil {
				tenantID = uint(id)
			}
		}
	}

	userID, ok := userIDValue.(uint)
	if !ok {
		// Thử chuyển đổi từ string
		if userIDStr, ok := userIDValue.(string); ok {
			if id, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
				userID = uint(id)
			}
		}
	}

	return tenantID, userID, nil
}
