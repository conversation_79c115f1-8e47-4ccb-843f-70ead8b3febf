// internal/pkg/database/gormdb/gorm_db.go
package gormdb

import (
	"wnapi/internal/pkg/database"
	"wnapi/internal/pkg/logger"

	"gorm.io/gorm"
)

// GORMDBManager là triển khai của DBManager sử dụng GORM
type GORMDBManager struct {
	db     *gorm.DB
	logger logger.Logger
}

// NewGORMDBManager tạo một instance mới của GORMDBManager
func NewGORMDBManager(
	dsnWrite string,
	dsnRead string,
	logger logger.Logger,
) (*database.DBManager, error) {
	// Stub implementation
	return database.NewDBManager(nil, nil, logger), nil
}
