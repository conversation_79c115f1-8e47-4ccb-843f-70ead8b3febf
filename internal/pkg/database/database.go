// internal/pkg/database/database.go
package database

import (
	"sync"

	"wnapi/internal/pkg/logger"

	"gorm.io/gorm"
)

// DBManager quản lý kết nối đến cơ sở dữ liệu
type DBManager struct {
	writeDB *gorm.DB
	readDB  *gorm.DB
	logger  logger.Logger
	mu      sync.RWMutex
}

// NewDBManager tạo một instance mới của DBManager
func NewDBManager(writeDB, readDB *gorm.DB, logger logger.Logger) *DBManager {
	return &DBManager{
		writeDB: writeDB,
		readDB:  readDB,
		logger:  logger,
	}
}

// DB trả về cơ sở dữ liệu ghi
func (m *DBManager) DB() *gorm.DB {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.writeDB
}

// ReadDB trả về cơ sở dữ liệu đọc
func (m *DBManager) ReadDB() *gorm.DB {
	m.mu.RLock()
	defer m.mu.RUnlock()
	if m.readDB != nil {
		return m.readDB
	}
	return m.writeDB
}

// WriteDB trả về cơ sở dữ liệu ghi
func (m *DBManager) WriteDB() *gorm.DB {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.writeDB
}
