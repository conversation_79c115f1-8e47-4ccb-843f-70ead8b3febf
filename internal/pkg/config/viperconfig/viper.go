// internal/pkg/config/viperconfig/viper.go
package viperconfig

import (
	"time"
	"wnapi/internal/pkg/config"
)

// ConfigLoader là trình nạp cấu hình sử dụng viper
type ConfigLoader struct {
}

// NewConfigLoader tạo một instance mới của ConfigLoader
func NewConfigLoader() *ConfigLoader {
	return &ConfigLoader{}
}

// Load nạp cấu hình từ file
func (l *ConfigLoader) Load(path string) (config.Config, error) {
	// Stub implementation
	return &ViperConfig{}, nil
}

// ViperConfig là một triển khai của Config sử dụng viper
type ViperConfig struct {
}

// Triển khai các phương thức của interface Config
func (c *ViperConfig) GetString(key string) string {
	return ""
}

func (c *ViperConfig) GetStringWithDefault(key string, defaultValue string) string {
	return defaultValue
}

func (c *ViperConfig) GetInt(key string) int {
	return 0
}

func (c *ViperConfig) GetIntWithDefault(key string, defaultValue int) int {
	return defaultValue
}

func (c *ViperConfig) GetBool(key string) bool {
	return false
}

func (c *ViperConfig) GetBoolWithDefault(key string, defaultValue bool) bool {
	return defaultValue
}

func (c *ViperConfig) GetDuration(key string) time.Duration {
	return 0
}

func (c *ViperConfig) GetDurationWithDefault(key string, defaultValue time.Duration) time.Duration {
	return defaultValue
}
