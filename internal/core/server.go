package core

import (
	"context"
	"fmt"
	"net/http"
	"wnapi/internal/pkg/logger"

	"github.com/gin-gonic/gin"
)

// Server là HTTP server của ứng dụng
type Server struct {
	app    *AppBootstrap
	engine *gin.Engine
	logger logger.Logger
	server *http.Server
}

// NewServer tạo một instance mới của server
func NewServer(app *AppBootstrap) *Server {
	engine := gin.Default()

	return &Server{
		app:    app,
		engine: engine,
		logger: app.Logger,
		server: &http.Server{
			Addr:    ":8080", // <PERSON><PERSON> thể lấy từ config
			Handler: engine,
		},
	}
}

// Start khởi động HTTP server
func (s *Server) Start() error {
	// Thiết lập routes
	s.SetupRoutes()

	// Log routes được đăng ký
	s.logRoutes()

	// Khởi động server
	s.logger.Info("Starting HTTP server", "address", s.server.Addr)
	return s.server.ListenAndServe()
}

// Shutdown tắt server an toàn
func (s *Server) Shutdown(ctx context.Context) error {
	s.logger.Info("Shutting down HTTP server")
	return s.server.Shutdown(ctx)
}

// SetupRoutes thiết lập routes cho server
func (s *Server) SetupRoutes() {
	// Root API group
	apiV1 := s.engine.Group("/api/v1")

	// Protected routes - yêu cầu xác thực và phân quyền
	protectedRoutes := apiV1.Group("")

	// 1. Áp dụng Tenant Middleware
	protectedRoutes.Use(s.tenantMiddleware())

	// 2. Áp dụng Authentication Middleware
	protectedRoutes.Use(s.authMiddleware())

	// Từ đây, các module sẽ đăng ký routes của chúng và áp dụng permission middleware
	// riêng cho từng endpoint
	s.registerModuleRoutes(protectedRoutes)
}

// tenantMiddleware trả về middleware kiểm tra tenant
func (s *Server) tenantMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Stub implementation
		// Trong thực tế, middleware này sẽ kiểm tra tenant từ request
		// và đặt thông tin tenant vào context

		c.Set("tenantID", uint(1)) // Giả lập tenant ID 1
		c.Next()
	}
}

// authMiddleware trả về middleware xác thực
func (s *Server) authMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Stub implementation
		// Trong thực tế, middleware này sẽ xác thực token
		// và đặt thông tin người dùng vào context

		c.Set("userID", uint(1)) // Giả lập user ID 1
		c.Next()
	}
}

// registerModuleRoutes đăng ký routes cho các module
func (s *Server) registerModuleRoutes(parentRouter *gin.RouterGroup) {
	// Lấy middleware factory từ app instance
	_ = s.app.GetMiddlewareFactory() // Sẽ được sử dụng khi tích hợp module thực tế

	// Đăng ký các module routes ở đây
	// Ví dụ:
	// productHandler := productModule.NewProductHandler(productService, mwFactory, s.logger)
	// productHandler.RegisterRoutes(parentRouter)
}

// logRoutes in ra danh sách routes đã đăng ký
func (s *Server) logRoutes() {
	routes := s.engine.Routes()
	s.logger.Info(fmt.Sprintf("Registered %d routes:", len(routes)))

	for _, route := range routes {
		s.logger.Info(fmt.Sprintf("%s %s", route.Method, route.Path))
	}
}
